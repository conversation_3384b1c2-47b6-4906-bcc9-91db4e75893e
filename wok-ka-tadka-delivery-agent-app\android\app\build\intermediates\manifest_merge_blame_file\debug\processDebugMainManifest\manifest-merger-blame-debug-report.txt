1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wokkatadka.deliveryapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:43:5-67
12-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:43:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:44:5-79
13-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:44:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:45:5-68
14-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:45:22-65
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:46:5-66
15-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:46:22-63
16
17    <!-- Optional permissions for future features -->
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:49:5-79
18-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:49:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:50:5-81
19-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:50:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:51:5-65
20-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:51:22-62
21
22    <!-- Features -->
23    <uses-feature
23-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:54:5-85
24        android:name="android.hardware.camera"
24-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:54:19-57
25        android:required="false" />
25-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:54:58-82
26    <uses-feature
26-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:55:5-87
27        android:name="android.hardware.location"
27-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:55:19-59
28        android:required="false" />
28-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:55:60-84
29    <uses-feature
29-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:56:5-91
30        android:name="android.hardware.location.gps"
30-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:56:19-63
31        android:required="false" />
31-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:56:64-88
32
33    <permission
33-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.wokkatadka.deliveryapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.wokkatadka.deliveryapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:4:5-40:19
40        android:allowBackup="true"
40-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:5:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
42        android:debuggable="true"
43        android:extractNativeLibs="false"
44        android:hardwareAccelerated="true"
44-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:11:9-43
45        android:icon="@mipmap/ic_launcher"
45-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:6:9-43
46        android:label="@string/app_name"
46-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:7:9-41
47        android:roundIcon="@mipmap/ic_launcher_round"
47-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:8:9-54
48        android:supportsRtl="true"
48-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:9:9-35
49        android:theme="@style/AppTheme"
49-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:10:9-40
50        android:usesCleartextTraffic="true" >
50-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:12:9-44
51        <activity
51-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:14:9-29:20
52            android:name="com.wokkatadka.deliveryapp.MainActivity"
52-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:16:13-41
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation|density"
53-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:15:13-148
54            android:exported="true"
54-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:20:13-36
55            android:label="@string/title_activity_main"
55-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:17:13-56
56            android:launchMode="singleTask"
56-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:19:13-44
57            android:screenOrientation="portrait"
57-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:21:13-49
58            android:theme="@style/AppTheme.NoActionBarLaunch"
58-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:18:13-62
59            android:windowSoftInputMode="adjustResize" >
59-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:22:13-55
60            <intent-filter>
60-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:24:13-27:29
61                <action android:name="android.intent.action.MAIN" />
61-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:25:17-69
61-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:25:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:26:17-77
63-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:26:27-74
64            </intent-filter>
65        </activity>
66
67        <provider
68            android:name="androidx.core.content.FileProvider"
68-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:32:13-62
69            android:authorities="com.wokkatadka.deliveryapp.fileprovider"
69-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:33:13-64
70            android:exported="false"
70-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:34:13-37
71            android:grantUriPermissions="true" >
71-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:35:13-47
72            <meta-data
72-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:36:13-38:64
73                android:name="android.support.FILE_PROVIDER_PATHS"
73-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:37:17-67
74                android:resource="@xml/file_paths" />
74-->C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\app\src\main\AndroidManifest.xml:38:17-51
75        </provider>
76        <provider
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
77            android:name="androidx.startup.InitializationProvider"
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
78            android:authorities="com.wokkatadka.deliveryapp.androidx-startup"
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
79            android:exported="false" >
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
80            <meta-data
80-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.emoji2.text.EmojiCompatInitializer"
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
82                android:value="androidx.startup" />
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
83            <meta-data
83-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
84                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
84-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
85                android:value="androidx.startup" />
85-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
86            <meta-data
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
88                android:value="androidx.startup" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
89        </provider>
90
91        <receiver
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
92            android:name="androidx.profileinstaller.ProfileInstallReceiver"
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
93            android:directBootAware="false"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
94            android:enabled="true"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
95            android:exported="true"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
96            android:permission="android.permission.DUMP" >
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
98                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
101                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
104                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
107                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
108            </intent-filter>
109        </receiver>
110    </application>
111
112</manifest>
