import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone, Lock, UserCheck, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Logo } from "@/components/ui/logo";

const AdminLogin = () => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [pin, setPin] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleAdminLogin = async () => {
    if (phoneNumber.length !== 10 || pin.length !== 4) {
      toast({
        title: "Invalid input",
        description: "Please enter a valid 10-digit phone number and 4-digit PIN",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // Default admin credentials: phone: 1234567890, pin: 0000
    if (phoneNumber === "1234567890" && pin === "0000") {
      setTimeout(() => {
        setIsLoading(false);
        toast({
          title: "Login successful",
          description: "Welcome to Admin Dashboard!",
        });
        navigate("/admin/dashboard");
      }, 1000);
    } else {
      setIsLoading(false);
      toast({
        title: "Invalid credentials",
        description: "Please check your admin credentials",
        variant: "destructive",
      });
    }
  };

  const handleGuestLogin = () => {
    toast({
      title: "Guest Access",
      description: "Logging in as guest user...",
    });
    // Store guest session info
    localStorage.setItem('currentUser', JSON.stringify({
      id: 'guest',
      name: 'Guest User',
      role: 'guest'
    }));
    navigate("/admin/dashboard");
  };

  return (
    <div className="apk-page-container bg-gradient-primary flex items-center justify-center p-4">
      <div className="w-full max-w-md animate-fade-in my-auto">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4 animate-pulse-soft">
            <Logo size="lg" variant="default" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Wok Ka Tadka</h1>
          <p className="text-white/90 text-lg">Restaurant Management</p>
        </div>

        <Card className="bg-white/95 backdrop-blur-sm shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl text-gray-900">Admin Login</CardTitle>
            <CardDescription className="text-gray-600">
              Sign in to manage your restaurant
            </CardDescription>
          </CardHeader>
          <CardContent className="px-6 pb-6">
            <div className="space-y-4">
              {/* Mobile Number Input */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-gray-700 flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Mobile Number
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="Enter 10 digit mobile number"
                  value={phoneNumber}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, "");
                    if (value.length <= 10) {
                      setPhoneNumber(value);
                    }
                  }}
                  className="h-12 text-lg"
                  maxLength={10}
                />
              </div>

              {/* PIN Input */}
              <div className="space-y-2">
                <Label htmlFor="pin" className="text-gray-700 flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Admin PIN
                </Label>
                <Input
                  id="pin"
                  type="password"
                  placeholder="Enter your 4 digit admin PIN"
                  value={pin}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, "");
                    if (value.length <= 4) {
                      setPin(value);
                    }
                  }}
                  className="h-12 text-lg text-center tracking-widest"
                  maxLength={4}
                />
              </div>

              {/* Admin Login Button */}
              <Button
                variant="delivery"
                size="lg"
                className="w-full h-12 mt-6"
                onClick={handleAdminLogin}
                disabled={isLoading || phoneNumber.length !== 10 || pin.length !== 4}
              >
                {isLoading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />}
                <UserCheck className="h-4 w-4 mr-2" />
                Login as Admin
              </Button>

              {/* Divider */}
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">Or</span>
                </div>
              </div>

              {/* Guest Login Button */}
              <Button
                variant="outline"
                size="lg"
                className="w-full h-12"
                onClick={handleGuestLogin}
              >
                <Users className="h-4 w-4 mr-2" />
                Login as Guest
              </Button>
            </div>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Default Admin: Phone: 1234567890, PIN: 0000
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminLogin;
