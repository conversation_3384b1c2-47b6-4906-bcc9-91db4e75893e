import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook to handle Escape key press for navigation
 * @param backPath - The path to navigate to when Escape is pressed
 * @param condition - Optional condition to check before handling escape (default: true)
 */
export const useEscapeKey = (backPath: string, condition: boolean = true) => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      // Don't handle if user is typing in an input or textarea
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Don't handle if condition is false
      if (!condition) {
        return;
      }

      if (event.key === 'Escape') {
        event.preventDefault();
        navigate(backPath);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [navigate, backPath, condition]);
};
