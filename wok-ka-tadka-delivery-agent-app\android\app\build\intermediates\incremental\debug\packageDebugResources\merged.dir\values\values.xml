<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorAccent">#f97316</color>
    <color name="colorPrimary">#ef4444</color>
    <color name="colorPrimaryDark">#dc2626</color>
    <color name="ic_launcher_background">#FFFFFF</color>
    <string name="app_name">Wok <PERSON></string>
    <string name="custom_url_scheme">com.wokkatadka.deliveryapp</string>
    <string name="package_name">com.wokkatadka.deliveryapp</string>
    <string name="title_activity_main">Wok Ka Tad<PERSON></string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
        
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/white</item>
    </style>
    <style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:background">@drawable/splash</item>
    </style>
</resources>