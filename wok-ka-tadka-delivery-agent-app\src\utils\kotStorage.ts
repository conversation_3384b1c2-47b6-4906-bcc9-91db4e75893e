// KOT Storage Utility for managing Kitchen Order Tickets

export interface KOTItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  veg: boolean;
  spicy: number;
  image: string;
  addedAt: string; // timestamp when item was added
}

export interface KOT {
  kotNumber: string;
  tableId: string;
  items: KOTItem[];
  specialInstructions: string;
  createdAt: string;
  updatedAt: string;
  totalAmount: number;
  status: 'active' | 'completed' | 'cancelled';
  versions: KOTVersion[]; // Track all additions to this KOT
}

export interface KOTVersion {
  versionNumber: number;
  addedItems: KOTItem[];
  addedAt: string;
  specialInstructions: string;
}

class KOTStorageManager {
  private readonly STORAGE_KEY = 'restaurant_kots';

  // Get all KOTs from localStorage
  getAllKOTs(): KOT[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error reading KOTs from storage:', error);
      return [];
    }
  }

  // Save KOTs to localStorage
  private saveKOTs(kots: KOT[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(kots));
    } catch (error) {
      console.error('Error saving KOTs to storage:', error);
    }
  }

  // Get active KOT for a specific table (returns first active KOT)
  getActiveKOTForTable(tableId: string): KOT | null {
    const kots = this.getAllKOTs();
    return kots.find(kot => kot.tableId === tableId && kot.status === 'active') || null;
  }

  // Get all active KOTs for a specific table (for multiple bills per table)
  getAllActiveKOTsForTable(tableId: string): KOT[] {
    const kots = this.getAllKOTs();
    return kots.filter(kot => kot.tableId === tableId && kot.status === 'active');
  }

  // Create a new KOT
  createKOT(tableId: string, items: KOTItem[], specialInstructions: string = ''): KOT {
    const kotNumber = `KOT${Date.now().toString().slice(-6)}_${Math.random().toString(36).substr(2, 3)}`;
    const now = new Date().toISOString();
    
    const newKOT: KOT = {
      kotNumber,
      tableId,
      items: items.map(item => ({
        ...item,
        addedAt: now
      })),
      specialInstructions,
      createdAt: now,
      updatedAt: now,
      totalAmount: this.calculateTotal(items),
      status: 'active',
      versions: [{
        versionNumber: 1,
        addedItems: items.map(item => ({
          ...item,
          addedAt: now
        })),
        addedAt: now,
        specialInstructions
      }]
    };

    const kots = this.getAllKOTs();
    kots.push(newKOT);
    this.saveKOTs(kots);
    
    return newKOT;
  }

  // Add items to existing KOT
  addItemsToKOT(kotNumber: string, newItems: KOTItem[], additionalInstructions: string = ''): KOT | null {
    const kots = this.getAllKOTs();
    const kotIndex = kots.findIndex(kot => kot.kotNumber === kotNumber);
    
    if (kotIndex === -1) {
      return null;
    }

    const kot = kots[kotIndex];
    const now = new Date().toISOString();
    
    // Add new items to existing items
    const itemsWithTimestamp = newItems.map(item => ({
      ...item,
      addedAt: now
    }));
    
    kot.items.push(...itemsWithTimestamp);
    kot.updatedAt = now;
    kot.totalAmount = this.calculateTotal(kot.items);
    
    // Update special instructions if provided
    if (additionalInstructions.trim()) {
      kot.specialInstructions = kot.specialInstructions 
        ? `${kot.specialInstructions}\n\nAdditional: ${additionalInstructions}`
        : additionalInstructions;
    }
    
    // Add new version
    const newVersion: KOTVersion = {
      versionNumber: kot.versions.length + 1,
      addedItems: itemsWithTimestamp,
      addedAt: now,
      specialInstructions: additionalInstructions
    };
    
    kot.versions.push(newVersion);
    
    kots[kotIndex] = kot;
    this.saveKOTs(kots);
    
    return kot;
  }

  // Complete a KOT (mark as completed)
  completeKOT(kotNumber: string): boolean {
    const kots = this.getAllKOTs();
    const kotIndex = kots.findIndex(kot => kot.kotNumber === kotNumber);
    
    if (kotIndex === -1) {
      return false;
    }
    
    kots[kotIndex].status = 'completed';
    kots[kotIndex].updatedAt = new Date().toISOString();
    this.saveKOTs(kots);
    
    return true;
  }

  // Cancel a KOT
  cancelKOT(kotNumber: string): boolean {
    const kots = this.getAllKOTs();
    const kotIndex = kots.findIndex(kot => kot.kotNumber === kotNumber);
    
    if (kotIndex === -1) {
      return false;
    }
    
    kots[kotIndex].status = 'cancelled';
    kots[kotIndex].updatedAt = new Date().toISOString();
    this.saveKOTs(kots);
    
    return true;
  }

  // Get KOT by number
  getKOTByNumber(kotNumber: string): KOT | null {
    const kots = this.getAllKOTs();
    return kots.find(kot => kot.kotNumber === kotNumber) || null;
  }

  // Calculate total amount for items
  private calculateTotal(items: KOTItem[]): number {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  }

  // Get KOT history for a table
  getKOTHistoryForTable(tableId: string): KOT[] {
    const kots = this.getAllKOTs();
    return kots.filter(kot => kot.tableId === tableId).sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  // Get complete bill for a table (all items from all versions)
  getCompleteBillForTable(tableId: string): {
    kotNumber: string;
    tableId: string;
    allItems: KOTItem[];
    totalAmount: number;
    status: 'active' | 'completed';
    createdAt: string;
    completedAt?: string;
    versions: any[];
    orderCount: number;
  } | null {
    const kot = this.getActiveKOTForTable(tableId);
    if (!kot) return null;

    // Combine all items from all versions
    const allItems: KOTItem[] = [];

    // Add items from all versions
    kot.versions.forEach(version => {
      allItems.push(...version.addedItems);
    });

    // Calculate total amount
    const totalAmount = allItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    return {
      kotNumber: kot.kotNumber,
      tableId: kot.tableId,
      allItems,
      totalAmount,
      status: kot.status,
      createdAt: kot.createdAt,
      completedAt: kot.completedAt,
      versions: kot.versions,
      orderCount: kot.versions.length
    };
  }

  // Clear all KOTs (for testing/reset purposes)
  clearAllKOTs(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }
}

// Export singleton instance
export const kotStorage = new KOTStorageManager();
