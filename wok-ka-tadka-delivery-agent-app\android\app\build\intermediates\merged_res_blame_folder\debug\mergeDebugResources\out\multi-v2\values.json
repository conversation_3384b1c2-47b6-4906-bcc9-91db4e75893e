{"logs": [{"outputFile": "com.wokkatadka.deliveryapp-mergeDebugResources-25:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\wok-ka-tadka-desktop-app\\wok-ka-tadka-desktop-app\\wok-ka-tadka-delivery-agent-app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "4,11,25", "startColumns": "4,4,4", "startOffsets": "93,413,1122", "endLines": "9,22,27", "endColumns": "12,12,12", "endOffsets": "407,1115,1268"}, "to": {"startLines": "337,343,355", "startColumns": "4,4,4", "startOffsets": "22077,22361,23034", "endLines": "342,354,357", "endColumns": "12,12,12", "endOffsets": "22356,23029,23180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f212eb2fcec7b76a8049b85cea08416b\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "279", "startColumns": "4", "startOffsets": "18067", "endColumns": "42", "endOffsets": "18105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ef234481c09f01fb9f0508a5da2b1126\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "7,8,10,11,12,13,199,200,201,202,203,204,205,288,612,613,614,1444,1446,1766,1775,1788", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "375,435,535,604,676,739,13471,13545,13621,13697,13774,13845,13914,18581,39839,39920,40012,92449,92558,116959,117419,118194", "endLines": "7,8,10,11,12,13,199,200,201,202,203,204,205,288,612,613,614,1445,1447,1774,1787,1791", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "430,489,599,671,734,806,13540,13616,13692,13769,13840,13909,13980,18644,39915,40007,40100,92553,92674,117414,118189,118462"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\wok-ka-tadka-desktop-app\\wok-ka-tadka-desktop-app\\wok-ka-tadka-delivery-agent-app\\android\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "3505", "endColumns": "56", "endOffsets": "3557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\998c918bf96ae2f6a4f5c8c644413a6f\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "281", "startColumns": "4", "startOffsets": "18170", "endColumns": "53", "endOffsets": "18219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\75881b531e34911967ea794bd3408c30\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "2,1901,2621,2627", "startColumns": "4,4,4,4", "startOffsets": "105,127265,151770,151981", "endLines": "2,1903,2626,2710", "endColumns": "60,12,24,24", "endOffsets": "161,127405,151976,156492"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\wok-ka-tadka-desktop-app\\wok-ka-tadka-desktop-app\\wok-ka-tadka-delivery-agent-app\\node_modules\\@capacitor\\android\\capacitor\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "5", "startColumns": "4", "startOffsets": "331", "endColumns": "79", "endOffsets": "406"}, "to": {"startLines": "327", "startColumns": "4", "startOffsets": "21302", "endColumns": "79", "endOffsets": "21377"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\wok-ka-tadka-desktop-app\\wok-ka-tadka-desktop-app\\wok-ka-tadka-delivery-agent-app\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "4,2,3", "startColumns": "4,4,4", "startOffsets": "153,55,102", "endColumns": "45,46,50", "endOffsets": "194,97,148"}, "to": {"startLines": "39,40,41", "startColumns": "4,4,4", "startOffsets": "2680,2726,2773", "endColumns": "45,46,50", "endOffsets": "2721,2768,2819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b5af023aa879967c1b3537c24628d5a4\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "259,280", "startColumns": "4,4", "startOffsets": "17023,18110", "endColumns": "41,59", "endOffsets": "17060,18165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0c35e3b0c2fe34519a603108fedf6f64\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "20585", "endColumns": "82", "endOffsets": "20663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5a75dca28172537968edb11f4713fc67\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "18224", "endColumns": "49", "endOffsets": "18269"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\wok-ka-tadka-desktop-app\\wok-ka-tadka-desktop-app\\wok-ka-tadka-delivery-agent-app\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,234,166,105", "endColumns": "49,72,67,60", "endOffsets": "100,302,229,161"}, "to": {"startLines": "318,326,328,331", "startColumns": "4,4,4,4", "startOffsets": "20668,21229,21382,21574", "endColumns": "49,72,67,60", "endOffsets": "20713,21297,21445,21630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "6,23,24,37,38,65,66,168,169,170,171,172,173,174,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,255,256,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,289,319,320,321,322,323,324,325,330,1719,1720,1724,1725,1729,1899,1900,2549,2583,2729,2764,2794,2827", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,1429,1501,2549,2614,4234,4303,11221,11291,11359,11431,11501,11562,11636,12493,12554,12615,12677,12741,12803,12864,12932,13032,13092,13158,13231,13300,13357,13409,14438,14510,14586,14651,14710,14769,14829,14889,14949,15009,15069,15129,15189,15249,15309,15369,15428,15488,15548,15608,15668,15728,15788,15848,15908,15968,16028,16087,16147,16207,16266,16325,16384,16443,16502,16857,16892,17176,17231,17294,17349,17407,17465,17526,17589,17646,17697,17747,17808,17865,17931,17965,18000,18649,20718,20785,20857,20926,20995,21069,21141,21503,113565,113682,113883,113993,114194,127126,127198,148539,150112,157069,158875,159875,160557", "endLines": "6,23,24,37,38,65,66,168,169,170,171,172,173,174,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,255,256,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,289,319,320,321,322,323,324,325,330,1719,1723,1724,1728,1729,1899,1900,2554,2592,2763,2784,2826,2832", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "370,1496,1584,2609,2675,4298,4361,11286,11354,11426,11496,11557,11631,11704,12549,12610,12672,12736,12798,12859,12927,13027,13087,13153,13226,13295,13352,13404,13466,14505,14581,14646,14705,14764,14824,14884,14944,15004,15064,15124,15184,15244,15304,15364,15423,15483,15543,15603,15663,15723,15783,15843,15903,15963,16023,16082,16142,16202,16261,16320,16379,16438,16497,16556,16887,16922,17226,17289,17344,17402,17460,17521,17584,17641,17692,17742,17803,17860,17926,17960,17995,18030,18714,20780,20852,20921,20990,21064,21136,21224,21569,113677,113878,113988,114189,114318,127193,127260,148737,150408,158870,159551,160552,160719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16884767c054ac4cab0f70a5a4855d4d\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2018,2034,2040,3097,3113", "startColumns": "4,4,4,4,4", "startOffsets": "131450,131875,132053,169190,169601", "endLines": "2033,2039,2049,3112,3116", "endColumns": "24,24,24,24,24", "endOffsets": "131870,132048,132332,169596,169723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a4193c6fbbe5e128015b7f6283124c0\\transformed\\fragment-1.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "253,260,283,2785,2790", "startColumns": "4,4,4,4,4", "startOffsets": "16766,17065,18274,159556,159726", "endLines": "253,260,283,2789,2793", "endColumns": "56,64,63,24,24", "endOffsets": "16818,17125,18333,159721,159870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,42,43,44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,175,176,177,178,179,180,181,182,183,206,207,208,209,210,211,212,213,249,250,251,252,254,257,258,261,278,284,285,286,287,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,329,332,333,334,335,336,358,366,367,371,375,379,384,390,397,401,405,410,414,418,422,426,430,434,440,444,450,454,460,464,469,473,476,480,486,490,496,500,506,509,513,517,521,525,529,530,531,532,535,538,541,544,548,549,550,551,552,555,557,559,561,566,567,571,577,581,582,584,596,597,601,607,611,615,616,620,647,651,652,656,684,856,882,1053,1079,1110,1118,1124,1140,1162,1167,1172,1182,1191,1200,1204,1211,1230,1237,1238,1247,1250,1253,1257,1261,1265,1268,1269,1274,1279,1289,1294,1301,1307,1308,1311,1315,1320,1322,1324,1327,1330,1332,1336,1339,1346,1349,1352,1356,1358,1362,1364,1366,1368,1372,1380,1388,1400,1406,1415,1418,1429,1432,1433,1438,1439,1448,1517,1587,1588,1598,1607,1608,1610,1614,1617,1620,1623,1626,1629,1632,1635,1639,1642,1645,1648,1652,1655,1659,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1685,1687,1688,1689,1690,1691,1692,1693,1694,1696,1697,1699,1700,1702,1704,1705,1707,1708,1709,1710,1711,1712,1714,1715,1716,1717,1718,1730,1732,1734,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1750,1751,1752,1753,1754,1755,1756,1758,1762,1792,1793,1794,1795,1796,1797,1801,1802,1803,1804,1806,1808,1810,1812,1814,1815,1816,1817,1819,1821,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1837,1838,1839,1840,1842,1844,1845,1847,1848,1850,1852,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1867,1868,1869,1870,1872,1873,1874,1875,1876,1878,1880,1882,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1904,1979,1982,1985,1988,2002,2008,2050,2053,2082,2109,2118,2182,2545,2555,2593,2711,2833,2857,2863,2869,2890,3014,3034,3040,3044,3050,3085,3117,3183,3203,3258,3270,3296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "166,221,266,494,811,866,928,992,1062,1123,1198,1274,1351,1589,1674,1756,1832,1908,1985,2063,2169,2275,2354,2434,2491,2824,2898,2973,3038,3104,3164,3225,3297,3370,3437,3562,3621,3680,3739,3798,3857,3911,3965,4018,4072,4126,4180,4366,4440,4519,4592,4666,4737,4809,4881,4954,5011,5069,5142,5216,5290,5365,5437,5510,5580,5651,5711,5772,5841,5910,5980,6054,6130,6194,6271,6347,6424,6489,6558,6635,6710,6779,6847,6924,6990,7051,7148,7213,7282,7381,7452,7511,7569,7626,7685,7749,7820,7892,7964,8036,8108,8175,8243,8311,8370,8433,8497,8587,8678,8738,8804,8871,8937,9007,9071,9124,9191,9252,9319,9432,9490,9553,9618,9683,9758,9831,9903,9947,9994,10040,10089,10150,10211,10272,10334,10398,10462,10526,10591,10654,10714,10775,10841,10900,10960,11022,11093,11153,11709,11795,11882,11972,12059,12147,12229,12312,12402,13985,14037,14095,14140,14206,14270,14327,14384,16561,16618,16666,16715,16823,16927,16974,17130,18035,18338,18402,18464,18524,18719,18793,18863,18941,18995,19065,19150,19198,19244,19305,19368,19434,19498,19569,19632,19697,19761,19822,19883,19935,20008,20082,20151,20226,20300,20374,20515,21450,21635,21713,21803,21891,21987,23185,23767,23856,24103,24384,24636,24921,25314,25791,26013,26235,26511,26738,26968,27198,27428,27658,27885,28304,28530,28955,29185,29613,29832,30115,30323,30454,30681,31107,31332,31759,31980,32405,32525,32801,33102,33426,33717,34031,34168,34299,34404,34646,34813,35017,35225,35496,35608,35720,35825,35942,36156,36302,36442,36528,36876,36964,37210,37628,37877,37959,38057,38714,38814,39066,39490,39745,40105,40194,40431,42455,42697,42799,43052,45208,55889,57405,68100,69628,71385,72011,72431,73692,74957,75213,75449,75996,76490,77095,77293,77873,79241,79616,79734,80272,80429,80625,80898,81154,81324,81465,81529,81894,82261,82937,83201,83539,83892,83986,84172,84478,84740,84865,84992,85231,85442,85561,85754,85931,86386,86567,86689,86948,87061,87248,87350,87457,87586,87861,88369,88865,89742,90036,90606,90755,91487,91659,91743,92079,92171,92679,97910,103281,103343,103921,104505,104596,104709,104938,105098,105250,105421,105587,105756,105923,106086,106329,106499,106672,106843,107117,107316,107521,107851,107935,108031,108127,108225,108325,108427,108529,108631,108733,108835,108935,109031,109143,109272,109395,109526,109657,109755,109869,109963,110103,110237,110333,110445,110545,110661,110757,110869,110969,111109,111245,111409,111539,111697,111847,111988,112132,112267,112379,112529,112657,112785,112921,113053,113183,113313,113425,114323,114469,114613,114751,114817,114907,114983,115087,115177,115279,115387,115495,115595,115675,115767,115865,115975,116027,116105,116211,116303,116407,116517,116639,116802,118467,118547,118647,118737,118847,118937,119178,119272,119378,119470,119570,119682,119796,119912,120028,120122,120236,120348,120450,120570,120692,120774,120878,120998,121124,121222,121316,121404,121516,121632,121754,121866,122041,122157,122243,122335,122447,122571,122638,122764,122832,122960,123104,123232,123301,123396,123511,123624,123723,123832,123943,124054,124155,124260,124360,124490,124581,124704,124798,124910,124996,125100,125196,125284,125402,125506,125610,125736,125824,125932,126032,126122,126232,126316,126418,126502,126556,126620,126726,126812,126922,127006,127410,130026,130144,130259,130339,130700,130933,132337,132415,133759,135120,135508,138351,148404,148742,150413,156497,160724,161475,161737,161937,162316,166594,167200,167429,167580,167795,168878,169728,172754,173498,175629,175969,177280", "endLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,42,43,44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,175,176,177,178,179,180,181,182,183,206,207,208,209,210,211,212,213,249,250,251,252,254,257,258,261,278,284,285,286,287,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,329,332,333,334,335,336,365,366,370,374,378,383,389,396,400,404,409,413,417,421,425,429,433,439,443,449,453,459,463,468,472,475,479,485,489,495,499,505,508,512,516,520,524,528,529,530,531,534,537,540,543,547,548,549,550,551,554,556,558,560,565,566,570,576,580,581,583,595,596,600,606,610,611,615,619,646,650,651,655,683,855,881,1052,1078,1109,1117,1123,1139,1161,1166,1171,1181,1190,1199,1203,1210,1229,1236,1237,1246,1249,1252,1256,1260,1264,1267,1268,1273,1278,1288,1293,1300,1306,1307,1310,1314,1319,1321,1323,1326,1329,1331,1335,1338,1345,1348,1351,1355,1357,1361,1363,1365,1367,1371,1379,1387,1399,1405,1414,1417,1428,1431,1432,1437,1438,1443,1516,1586,1587,1597,1606,1607,1609,1613,1616,1619,1622,1625,1628,1631,1634,1638,1641,1644,1647,1651,1654,1658,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1684,1686,1687,1688,1689,1690,1691,1692,1693,1695,1696,1698,1699,1701,1703,1704,1706,1707,1708,1709,1710,1711,1713,1714,1715,1716,1717,1718,1731,1733,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1749,1750,1751,1752,1753,1754,1755,1757,1761,1765,1792,1793,1794,1795,1796,1800,1801,1802,1803,1805,1807,1809,1811,1813,1814,1815,1816,1818,1820,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1836,1837,1838,1839,1841,1843,1844,1846,1847,1849,1851,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1866,1867,1868,1869,1871,1872,1873,1874,1875,1877,1879,1881,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1978,1981,1984,1987,2001,2007,2017,2052,2081,2108,2117,2181,2544,2548,2582,2620,2728,2856,2862,2868,2889,3013,3033,3039,3043,3049,3084,3096,3182,3202,3257,3269,3295,3302", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "216,261,310,530,861,923,987,1057,1118,1193,1269,1346,1424,1669,1751,1827,1903,1980,2058,2164,2270,2349,2429,2486,2544,2893,2968,3033,3099,3159,3220,3292,3365,3432,3500,3616,3675,3734,3793,3852,3906,3960,4013,4067,4121,4175,4229,4435,4514,4587,4661,4732,4804,4876,4949,5006,5064,5137,5211,5285,5360,5432,5505,5575,5646,5706,5767,5836,5905,5975,6049,6125,6189,6266,6342,6419,6484,6553,6630,6705,6774,6842,6919,6985,7046,7143,7208,7277,7376,7447,7506,7564,7621,7680,7744,7815,7887,7959,8031,8103,8170,8238,8306,8365,8428,8492,8582,8673,8733,8799,8866,8932,9002,9066,9119,9186,9247,9314,9427,9485,9548,9613,9678,9753,9826,9898,9942,9989,10035,10084,10145,10206,10267,10329,10393,10457,10521,10586,10649,10709,10770,10836,10895,10955,11017,11088,11148,11216,11790,11877,11967,12054,12142,12224,12307,12397,12488,14032,14090,14135,14201,14265,14322,14379,14433,16613,16661,16710,16761,16852,16969,17018,17171,18062,18397,18459,18519,18576,18788,18858,18936,18990,19060,19145,19193,19239,19300,19363,19429,19493,19564,19627,19692,19756,19817,19878,19930,20003,20077,20146,20221,20295,20369,20510,20580,21498,21708,21798,21886,21982,22072,23762,23851,24098,24379,24631,24916,25309,25786,26008,26230,26506,26733,26963,27193,27423,27653,27880,28299,28525,28950,29180,29608,29827,30110,30318,30449,30676,31102,31327,31754,31975,32400,32520,32796,33097,33421,33712,34026,34163,34294,34399,34641,34808,35012,35220,35491,35603,35715,35820,35937,36151,36297,36437,36523,36871,36959,37205,37623,37872,37954,38052,38709,38809,39061,39485,39740,39834,40189,40426,42450,42692,42794,43047,45203,55884,57400,68095,69623,71380,72006,72426,73687,74952,75208,75444,75991,76485,77090,77288,77868,79236,79611,79729,80267,80424,80620,80893,81149,81319,81460,81524,81889,82256,82932,83196,83534,83887,83981,84167,84473,84735,84860,84987,85226,85437,85556,85749,85926,86381,86562,86684,86943,87056,87243,87345,87452,87581,87856,88364,88860,89737,90031,90601,90750,91482,91654,91738,92074,92166,92444,97905,103276,103338,103916,104500,104591,104704,104933,105093,105245,105416,105582,105751,105918,106081,106324,106494,106667,106838,107112,107311,107516,107846,107930,108026,108122,108220,108320,108422,108524,108626,108728,108830,108930,109026,109138,109267,109390,109521,109652,109750,109864,109958,110098,110232,110328,110440,110540,110656,110752,110864,110964,111104,111240,111404,111534,111692,111842,111983,112127,112262,112374,112524,112652,112780,112916,113048,113178,113308,113420,113560,114464,114608,114746,114812,114902,114978,115082,115172,115274,115382,115490,115590,115670,115762,115860,115970,116022,116100,116206,116298,116402,116512,116634,116797,116954,118542,118642,118732,118842,118932,119173,119267,119373,119465,119565,119677,119791,119907,120023,120117,120231,120343,120445,120565,120687,120769,120873,120993,121119,121217,121311,121399,121511,121627,121749,121861,122036,122152,122238,122330,122442,122566,122633,122759,122827,122955,123099,123227,123296,123391,123506,123619,123718,123827,123938,124049,124150,124255,124355,124485,124576,124699,124793,124905,124991,125095,125191,125279,125397,125501,125605,125731,125819,125927,126027,126117,126227,126311,126413,126497,126551,126615,126721,126807,126917,127001,127121,130021,130139,130254,130334,130695,130928,131445,132410,133754,135115,135503,138346,148399,148534,150107,151765,157064,161470,161732,161932,162311,166589,167195,167424,167575,167790,168873,169185,172749,173493,175624,175964,177275,177478"}}]}]}