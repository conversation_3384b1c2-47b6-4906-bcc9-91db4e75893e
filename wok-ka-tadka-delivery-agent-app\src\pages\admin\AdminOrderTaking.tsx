import { useState, useEffect, useCallback, useRef } from "react";
import { useEscapeKey } from "@/hooks/useEscapeKey";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  Plus,
  Minus,
  ShoppingCart,
  Search,
  Filter,
  FileText,
  CheckCircle,
  Star,
  Clock,
  Leaf,
  X
} from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { completeMenuItems } from "@/data/completeMenuData";
import { kotStorage, KOTItem } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";
import { searchMenuItems } from "@/utils/shortcodeSearch";
import PrintService from "@/services/printService";

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
  isVeg: boolean;
  isEgg?: boolean;
  isPopular?: boolean;
  isBestseller?: boolean;
  spicy?: number;
}

const AdminOrderTaking = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();

  // Get table ID from URL params
  const tableId = searchParams.get('table') || '';

  const [searchQuery, setSearchQuery] = useState("");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [existingKOT, setExistingKOT] = useState<any>(null);
  const [showKOTOptionsDialog, setShowKOTOptionsDialog] = useState(false);

  // Keyboard navigation state
  const [selectedItemIndex, setSelectedItemIndex] = useState(0);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const menuContainerRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLDivElement>(null);

  // Simple filter state matching waiter dashboard exactly
  const [selectedFilters, setSelectedFilters] = useState({
    allItems: true,
    popular: false,
    bestseller: false,
    veg: false,
    nonVeg: false,
    egg: false,
  });

  // Global Escape key handler for back navigation
  useEscapeKey("/admin/tables", !isSearchFocused);

  // Check for existing KOT when component mounts
  useEffect(() => {
    if (tableId) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (activeKOT) {
        setExistingKOT(activeKOT);
      }
    }
  }, [tableId]);

  // Get all items from all categories and add missing properties - matching waiter dashboard exactly
  const allItems = Object.entries(completeMenuItems).flatMap(([categoryKey, items]) =>
    items.map(item => ({
      ...item,
      id: item.id || `${categoryKey}-${item.name.replace(/\s+/g, '-').toLowerCase()}`,
      category: categoryKey,
      image: item.image || `/Menu_Images/${item.name.replace(/\s+/g, '_')}.jpg`,
      isVeg: item.isVeg !== undefined ? item.isVeg : true,
      isEgg: item.isEgg || false,
      isPopular: item.isPopular || false,
      isBestseller: item.isBestseller || false,
      preparationTime: item.preparationTime || 15
    }))
  );

  // Filter items based on selected filters - exact same logic as waiter dashboard
  const filteredItems = allItems.filter(item => {
    // Use shortcode search for better matching
    const matchesSearch = searchQuery.trim() === '' ||
      searchMenuItems([item], searchQuery, 'name').length > 0;

    // Apply filter logic based on selected filters
    if (selectedFilters.allItems) return matchesSearch;

    let matchesFilter = false;

    // Special categories - exact same as waiter dashboard
    if (selectedFilters.popular && item.isPopular) matchesFilter = true;
    if (selectedFilters.bestseller && item.isBestseller) matchesFilter = true;
    if (selectedFilters.veg && item.isVeg && !item.isEgg) matchesFilter = true;
    if (selectedFilters.nonVeg && !item.isVeg && !item.isEgg) matchesFilter = true;
    if (selectedFilters.egg && item.isEgg) matchesFilter = true;

    return matchesSearch && matchesFilter;
  });

  // Filter handling - exact same as waiter dashboard
  const handleFilterChange = (filterKey: string, checked: boolean) => {
    if (filterKey === 'allItems') {
      setSelectedFilters({
        allItems: true,
        popular: false,
        bestseller: false,
        veg: false,
        nonVeg: false,
        egg: false,
      });
    } else {
      setSelectedFilters(prev => ({
        ...prev,
        allItems: false,
        [filterKey]: checked
      }));
    }
  };

  const clearAllFilters = () => {
    setSelectedFilters({
      allItems: true,
      popular: false,
      bestseller: false,
      veg: false,
      nonVeg: false,
      egg: false,
    });
  };
  // Cart functions - exact same as waiter dashboard
  const addToCart = (item: any) => {
    const existingItem = cart.find(cartItem => cartItem.id === item.id);
    if (existingItem) {
      setCart(cart.map(cartItem =>
        cartItem.id === item.id
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      ));
    } else {
      setCart([...cart, { ...item, quantity: 1 }]);
    }
  };

  const removeFromCart = (itemId: string) => {
    const existingItem = cart.find(cartItem => cartItem.id === itemId);
    if (existingItem && existingItem.quantity > 1) {
      setCart(cart.map(cartItem =>
        cartItem.id === itemId
          ? { ...cartItem, quantity: cartItem.quantity - 1 }
          : cartItem
      ));
    } else {
      setCart(cart.filter(cartItem => cartItem.id !== itemId));
    }
  };

  const getItemQuantity = (itemId: string) => {
    const item = cart.find(cartItem => cartItem.id === itemId);
    return item ? item.quantity : 0;
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // Function to scroll selected item into view
  const scrollSelectedItemIntoView = useCallback(() => {
    // Use requestAnimationFrame to ensure DOM has updated
    requestAnimationFrame(() => {
      if (selectedItemRef.current && menuContainerRef.current) {
        selectedItemRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }
    });
  }, []);

  // Update cart item quantity
  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
    } else {
      setCart(cart.map(cartItem =>
        cartItem.id === itemId
          ? { ...cartItem, quantity: newQuantity }
          : cartItem
      ));
    }
  };

  // Keyboard navigation handlers
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't handle shortcuts if user is typing in search or filter is open
    if (isSearchFocused || isFilterOpen) {
      return;
    }

    // Ctrl + S to focus search
    if (event.ctrlKey && event.key === 's') {
      event.preventDefault();
      searchInputRef.current?.focus();
      setIsSearchFocused(true);
      return;
    }

    // Ctrl + K to print KOT and go to table management
    if (event.ctrlKey && event.key === 'k') {
      event.preventDefault();
      if (cart.length > 0) {
        handlePrintKOT();
      }
      return;
    }

    // Arrow key navigation for menu items (without Ctrl)
    if (!event.ctrlKey && event.key === 'ArrowUp') {
      event.preventDefault();
      setSelectedItemIndex(prev => Math.max(0, prev - 3)); // Move up one row (3 items per row)
      return;
    }

    if (!event.ctrlKey && event.key === 'ArrowDown') {
      event.preventDefault();
      setSelectedItemIndex(prev => Math.min(filteredItems.length - 1, prev + 3)); // Move down one row (3 items per row)
      return;
    }

    if (!event.ctrlKey && event.key === 'ArrowLeft') {
      event.preventDefault();
      setSelectedItemIndex(prev => Math.max(0, prev - 1)); // Move left one item
      return;
    }

    if (!event.ctrlKey && event.key === 'ArrowRight') {
      event.preventDefault();
      setSelectedItemIndex(prev => Math.min(filteredItems.length - 1, prev + 1)); // Move right one item
      return;
    }

    // Enter to add selected item to cart
    if (event.key === 'Enter') {
      event.preventDefault();
      if (filteredItems[selectedItemIndex]) {
        addToCart(filteredItems[selectedItemIndex]);
      }
      return;
    }

    // Ctrl + Right/Left to increase/decrease quantity of selected item
    if (event.ctrlKey && (event.key === 'ArrowRight' || event.key === 'ArrowLeft')) {
      event.preventDefault();
      const selectedItem = filteredItems[selectedItemIndex];
      if (selectedItem) {
        const cartItem = cart.find(item => item.id === selectedItem.id);

        if (event.key === 'ArrowRight') {
          // Increase quantity (add to cart if not present)
          if (cartItem) {
            updateCartItemQuantity(selectedItem.id, cartItem.quantity + 1);
          } else {
            // Add item to cart with quantity 1
            addToCart(selectedItem);
          }
        } else if (event.key === 'ArrowLeft') {
          // Decrease quantity
          if (cartItem) {
            if (cartItem.quantity > 1) {
              updateCartItemQuantity(selectedItem.id, cartItem.quantity - 1);
            } else {
              // Remove item if quantity would be 0
              removeFromCart(selectedItem.id);
            }
          }
          // If item not in cart, do nothing for left arrow
        }
      }
      return;
    }
  }, [isSearchFocused, isFilterOpen, cart, selectedItemIndex, filteredItems]);

  const handlePrintKOT = async () => {
    if (cart.length === 0) {
      toast({
        title: "Cart is empty",
        description: "Please add items to cart before printing KOT",
        variant: "destructive",
      });
      return;
    }

    // Check if there's an existing KOT for this table
    if (existingKOT) {
      // Show dialog to choose between adding to existing KOT or creating new bill
      setShowKOTOptionsDialog(true);
      return;
    }

    // If no existing KOT, create new one directly
    await createNewKOT();
  };

  const createNewKOT = async () => {
    try {
      // Convert cart items to KOT items
      const kotItems: KOTItem[] = cart.map((item: any) => ({
        id: parseInt(item.id) || Date.now() + Math.random(), // Convert string id to number
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        veg: item.isVeg || item.veg || false, // Handle both isVeg and veg properties
        spicy: item.spicy || 0,
        image: item.image || "/img/app.png",
        addedAt: new Date().toISOString()
      }));

      // Create KOT
      const result = kotStorage.createKOT(tableId, kotItems, "");

      if (result) {
        // Print the KOT using thermal printer
        const success = await PrintService.printKOT(result);

        if (success) {
          toast({
            title: "KOT Generated & Printed!",
            description: `KOT #${result.kotNumber} created and sent to kitchen for Table ${tableId}`,
          });
        } else {
          toast({
            title: "KOT Generated",
            description: `KOT #${result.kotNumber} created but printing failed. Please try printing manually.`,
            variant: "destructive"
          });
        }

        // Clear cart after successful KOT creation
        setCart([]);

        // Navigate back to table management
        setTimeout(() => {
          navigate("/admin/tables");
        }, 1000);
      } else {
        throw new Error("Failed to create KOT");
      }
    } catch (error) {
      console.error("Error creating KOT:", error);
      toast({
        title: "Error",
        description: "Failed to create KOT. Please try again.",
        variant: "destructive",
      });
    }
  };

  const addToExistingKOT = async () => {
    try {
      // Convert cart items to KOT items
      const kotItems: KOTItem[] = cart.map((item: any) => ({
        id: parseInt(item.id) || Date.now() + Math.random(),
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        veg: item.isVeg || item.veg || false,
        spicy: item.spicy || 0,
        image: item.image || "/img/app.png",
        addedAt: new Date().toISOString()
      }));

      // Add items to existing KOT
      const updatedKOT = kotStorage.addItemsToKOT(existingKOT.kotNumber, kotItems, "");

      if (updatedKOT) {
        // Print the updated KOT
        const success = await PrintService.printKOT(updatedKOT);

        if (success) {
          toast({
            title: "Items Added & KOT Printed!",
            description: `Items added to existing KOT #${existingKOT.kotNumber} and sent to kitchen`,
          });
        } else {
          toast({
            title: "Items Added",
            description: `Items added to KOT #${existingKOT.kotNumber} but printing failed. Please try printing manually.`,
            variant: "destructive"
          });
        }

        // Clear cart and close dialog
        setCart([]);
        setShowKOTOptionsDialog(false);

        // Navigate back to table management
        setTimeout(() => {
          navigate("/admin/tables");
        }, 1000);
      } else {
        throw new Error("Failed to add items to existing KOT");
      }
    } catch (error) {
      console.error("Error adding items to existing KOT:", error);
      toast({
        title: "Error",
        description: "Failed to add items to existing KOT. Please try again.",
        variant: "destructive",
      });
    }
  };

  const createNewBill = async () => {
    // Close the dialog first
    setShowKOTOptionsDialog(false);

    // Create a new KOT (which will be a separate bill)
    await createNewKOT();
  };



  // Keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Reset selected index when filtered items change
  useEffect(() => {
    setSelectedItemIndex(0);
  }, [filteredItems.length, searchQuery, selectedFilters]);

  // Scroll selected item into view when selectedItemIndex changes
  useEffect(() => {
    scrollSelectedItemIntoView();
  }, [selectedItemIndex]);







  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20 rounded-full"
              onClick={() => navigate("/admin/tables")}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold">Table {tableId}</h1>
              <p className="text-white/80 text-sm">Take Order</p>
            </div>
          </div>

          {/* Search Bar in Header */}
          <div className="flex-1 max-w-md mx-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-4 w-4" />
              <Input
                ref={searchInputRef}
                placeholder="Press Ctrl+S to search... (e.g., 'cb' for Chicken Biryani)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                onKeyDown={(e) => {
                  if (e.key === 'Escape') {
                    e.preventDefault();
                    setIsSearchFocused(false);
                    searchInputRef.current?.blur();
                  }
                  // Allow arrow keys to work for navigation when search is focused
                  if (e.key.startsWith('Arrow') && !e.ctrlKey) {
                    e.preventDefault();
                    setIsSearchFocused(false);
                    searchInputRef.current?.blur();
                    // Let the global handler take over
                    document.dispatchEvent(new KeyboardEvent('keydown', {
                      key: e.key,
                      ctrlKey: e.ctrlKey,
                      shiftKey: e.shiftKey,
                      altKey: e.altKey
                    }));
                  }
                }}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:bg-white/20"
              />
            </div>
          </div>

          {/* Keyboard Shortcuts Help */}
          <div className="text-xs text-white/80 text-right">
            <div>Ctrl+S: Search • Ctrl+K: Print KOT</div>
            <div>Arrows: Navigate • Enter: Add • Esc: Back</div>
          </div>
        </div>
      </div>

      {/* Main Content - 3 Column Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Filters */}
        <div className="w-64 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filter Categories
            </h3>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allItems"
                  checked={selectedFilters.allItems}
                  onCheckedChange={(checked) => setSelectedFilters({
                    ...selectedFilters,
                    allItems: checked as boolean,
                    popular: false,
                    bestseller: false,
                    veg: false,
                    nonVeg: false,
                    egg: false,
                  })}
                />
                <Label htmlFor="allItems" className="text-sm font-medium">All Items</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="popular"
                  checked={selectedFilters.popular}
                  onCheckedChange={(checked) => setSelectedFilters({
                    ...selectedFilters,
                    allItems: false,
                    popular: checked as boolean
                  })}
                />
                <Label htmlFor="popular" className="text-sm">Popular Items</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bestseller"
                  checked={selectedFilters.bestseller}
                  onCheckedChange={(checked) => setSelectedFilters({
                    ...selectedFilters,
                    allItems: false,
                    bestseller: checked as boolean
                  })}
                />
                <Label htmlFor="bestseller" className="text-sm">Bestsellers</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="veg"
                  checked={selectedFilters.veg}
                  onCheckedChange={(checked) => setSelectedFilters({
                    ...selectedFilters,
                    allItems: false,
                    veg: checked as boolean
                  })}
                />
                <Label htmlFor="veg" className="text-sm flex items-center gap-1">
                  <div className="w-3 h-3 border border-green-500 flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  </div>
                  Vegetarian
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="nonVeg"
                  checked={selectedFilters.nonVeg}
                  onCheckedChange={(checked) => setSelectedFilters({
                    ...selectedFilters,
                    allItems: false,
                    nonVeg: checked as boolean
                  })}
                />
                <Label htmlFor="nonVeg" className="text-sm flex items-center gap-1">
                  <div className="w-3 h-3 border border-red-500 flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                  </div>
                  Non-Vegetarian
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="egg"
                  checked={selectedFilters.egg}
                  onCheckedChange={(checked) => setSelectedFilters({
                    ...selectedFilters,
                    allItems: false,
                    egg: checked as boolean
                  })}
                />
                <Label htmlFor="egg" className="text-sm">Egg Items</Label>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="w-full mt-4"
              onClick={() => setSelectedFilters({
                allItems: true,
                popular: false,
                bestseller: false,
                veg: false,
                nonVeg: false,
                egg: false,
              })}
            >
              Clear All Filters
            </Button>
          </div>
        </div>

        {/* Center - Menu Items */}
        <div
          ref={menuContainerRef}
          className="flex-1 overflow-y-auto bg-gray-50 p-4"
          style={{
            scrollBehavior: 'smooth',
            maxHeight: 'calc(100vh - 120px)' // Account for header height
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredItems.map((item, index) => (
              <Card
                key={item.id}
                ref={index === selectedItemIndex ? selectedItemRef : null}
                className={`shadow-lg border-2 bg-white overflow-hidden transition-all duration-200 hover:shadow-xl cursor-pointer ${
                  index === selectedItemIndex
                    ? 'border-orange-500 ring-2 ring-orange-200 bg-orange-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => {
                  setSelectedItemIndex(index);
                  addToCart(item);
                }}
              >
                <CardContent className="p-0">
                  <div className="relative">
                    {/* Item Image */}
                    <div className="w-full h-48 bg-gradient-to-br from-orange-100 to-red-100">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/img/app.png';
                        }}
                      />
                    </div>

                    {/* Selected indicator */}
                    {index === selectedItemIndex && (
                      <div className="absolute top-2 right-2 bg-orange-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                        ✓
                      </div>
                    )}

                    {/* Veg/Non-veg indicator */}
                    <div className="absolute top-2 left-2">
                      {item.isVeg ? (
                        <div className="w-5 h-5 border-2 border-green-500 bg-white flex items-center justify-center rounded">
                          <div className="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
                        </div>
                      ) : item.isEgg ? (
                        <div className="w-5 h-5 border-2 border-yellow-500 bg-white flex items-center justify-center rounded">
                          <div className="w-2.5 h-2.5 bg-yellow-500 rounded-full"></div>
                        </div>
                      ) : (
                        <div className="w-5 h-5 border-2 border-red-500 bg-white flex items-center justify-center rounded">
                          <div className="w-2.5 h-2.5 bg-red-500 rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="mb-2">
                      <h3 className="font-semibold text-gray-900 text-base mb-1 line-clamp-2">
                        {item.name}
                      </h3>

                      <div className="flex items-center gap-2 mb-2">
                        {item.isPopular && (
                          <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs px-2 py-1">
                            🔥 Popular
                          </Badge>
                        )}
                        {item.isBestseller && (
                          <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs px-2 py-1">
                            ⭐ Bestseller
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-3 text-xs text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span>{item.rating || 4.5}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{item.preparationTime || 15} mins</span>
                        </div>
                      </div>

                      <div className="text-xl font-bold text-orange-600 mb-3">₹{item.price}</div>
                    </div>

                    {/* Add to Cart Section */}
                    <div className="flex items-center justify-between">
                      {getItemQuantity(item.id) === 0 ? (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            addToCart(item);
                          }}
                          className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add to Cart
                        </Button>
                      ) : (
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2 bg-orange-500 text-white rounded-lg px-3 py-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                updateCartItemQuantity(item.id, getItemQuantity(item.id) - 1);
                              }}
                              className="h-6 w-6 text-white hover:bg-white/20 rounded"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="text-sm font-bold min-w-[24px] text-center">
                              {getItemQuantity(item.id)}
                            </span>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                addToCart(item);
                              }}
                              className="h-6 w-6 text-white hover:bg-white/20 rounded"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>

                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No items found</h3>
              <p className="text-gray-600">Try searching with different keywords or adjust filters</p>
            </div>
          )}
        </div>

        {/* Right Sidebar - Cart */}
        <div className="w-80 bg-white shadow-lg border-l border-gray-200 overflow-y-auto">
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <ShoppingCart className="h-4 w-4" />
              Cart ({getTotalItems()} items)
            </h3>

            {cart.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">Your cart is empty</p>
                <p className="text-gray-400 text-xs mt-1">Add items to get started</p>
              </div>
            ) : (
              <>
                <div className="space-y-3 mb-4">
                  {cart.map((item) => (
                    <div key={item.id} className="bg-gray-50 rounded-lg p-3 border">
                      <div className="flex items-start gap-3">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-12 h-12 object-cover rounded-lg"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/img/app.png';
                          }}
                        />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 text-sm line-clamp-2 mb-1">
                            {item.name}
                          </h4>
                          <div className="flex items-center justify-between">
                            <span className="text-orange-600 font-semibold">₹{item.price}</span>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => updateCartItemQuantity(item.id, item.quantity - 1)}
                                className="h-6 w-6 border-gray-300"
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="font-medium text-sm min-w-[20px] text-center">
                                {item.quantity}
                              </span>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => addToCart(item)}
                                className="h-6 w-6 border-gray-300"
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Subtotal: ₹{item.price * item.quantity}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Cart Summary */}
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-700">Total Items:</span>
                    <span className="font-semibold">{getTotalItems()}</span>
                  </div>
                  <div className="flex justify-between items-center mb-4">
                    <span className="font-medium text-gray-700">Total Amount:</span>
                    <span className="font-bold text-xl text-orange-600">₹{getTotalAmount()}</span>
                  </div>

                  <Button
                    variant="default"
                    size="lg"
                    onClick={handlePrintKOT}
                    className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-3"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    {existingKOT ? "Print KOT - Choose Option (Ctrl+K)" : "Print KOT (Ctrl+K)"}
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Existing KOT Alert - Now integrated in cart */}
      {existingKOT && (
        <div className="fixed top-20 right-4 bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-300 p-3 rounded-lg shadow-lg z-50 max-w-sm">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-blue-900">
              🍽️ Active Order
            </span>
            <Badge variant="secondary" className="bg-blue-200 text-blue-800 text-xs">
              KOT #{existingKOT.kotNumber}
            </Badge>
          </div>
          <div className="text-xs text-blue-700 mb-3">
            📋 {existingKOT.items.length} items • 💰 ₹{existingKOT.totalAmount}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => {
                navigate(`/admin/kot/${tableId}`, {
                  state: { existingKOT, viewMode: true, tableId }
                });
              }}
            >
              View Order
            </Button>
            <Button
              variant="default"
              size="sm"
              className="flex-1 text-xs bg-green-600 hover:bg-green-700"
              onClick={() => {
                kotStorage.completeKOT(existingKOT.kotNumber);
                setExistingKOT(null);
                toast({
                  title: "Order Completed!",
                  description: `Table ${tableId} order completed`,
                });
              }}
            >
              Complete
            </Button>
          </div>
        </div>
      )}

      {/* KOT Options Dialog */}
      <Dialog open={showKOTOptionsDialog} onOpenChange={setShowKOTOptionsDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-orange-500" />
              KOT Options for Table {tableId}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-semibold text-blue-900">Existing Order</span>
                <Badge variant="secondary" className="bg-blue-200 text-blue-800 text-xs">
                  KOT #{existingKOT?.kotNumber}
                </Badge>
              </div>
              <div className="text-xs text-blue-700">
                {existingKOT?.items.length} items • ₹{existingKOT?.totalAmount}
              </div>
            </div>

            <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
              <div className="flex items-center gap-2 mb-2">
                <ShoppingCart className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-semibold text-orange-900">New Items</span>
              </div>
              <div className="text-xs text-orange-700">
                {getTotalItems()} items • ₹{getTotalAmount()}
              </div>
            </div>

            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
              <p className="font-medium mb-1">Choose an option:</p>
              <ul className="text-xs space-y-1">
                <li>• <strong>Add to Existing KOT:</strong> Combine with current order (single bill)</li>
                <li>• <strong>Create New Bill:</strong> Separate bill for new items (multiple bills per table)</li>
              </ul>
            </div>

            <div className="flex flex-col gap-2">
              <Button
                onClick={addToExistingKOT}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add to Existing KOT #{existingKOT?.kotNumber}
              </Button>
              <Button
                onClick={createNewBill}
                variant="outline"
                className="w-full border-orange-300 text-orange-700 hover:bg-orange-50"
              >
                <FileText className="h-4 w-4 mr-2" />
                Create New Bill
              </Button>
              <Button
                onClick={() => setShowKOTOptionsDialog(false)}
                variant="ghost"
                className="w-full text-gray-500 hover:bg-gray-100"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  );
};

export default AdminOrderTaking;

