import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  ShoppingCart,
  Users,
  Package,
  TrendingUp,
  TrendingDown,
  Clock,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Calendar,
  Utensils,
  FileText,
  Settings,
  LogOut,
  Gift,
  Smartphone,
  Bell,
  ChefHat,
  Car
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { useToast } from "@/hooks/use-toast";
import { sampleDataUtils } from "@/utils/initializeSampleData";
import MultiPlatformNotifications from "@/components/MultiPlatformNotifications";
import { multiPlatformOrderManager, PlatformStats } from "@/utils/multiPlatformOrders";
import { getPendingNotificationsCount } from "@/utils/notificationStorage";
import { customerOrderManager } from "@/utils/customerOrdersStorage";
import { runOrderIntegrationTest } from "@/utils/testOrderIntegration";
import NotificationPanel from "@/components/NotificationPanel";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [platformStats, setPlatformStats] = useState<PlatformStats[]>([]);
  const [pendingNotifications, setPendingNotifications] = useState(0);
  const [customerOrdersCount, setCustomerOrdersCount] = useState(0);
  const [isNotificationPanelVisible, setIsNotificationPanelVisible] = useState(true);

  useEffect(() => {
    loadPlatformStats();
    loadPendingNotifications();
    loadCustomerOrdersCount();

    // Refresh stats every 30 seconds
    const interval = setInterval(() => {
      loadPlatformStats();
      loadPendingNotifications();
      loadCustomerOrdersCount();
    }, 30000);

    // Listen for notification changes
    const handleNotificationsChanged = () => {
      loadPendingNotifications();
    };

    window.addEventListener('notificationsChanged', handleNotificationsChanged);

    return () => {
      clearInterval(interval);
      window.removeEventListener('notificationsChanged', handleNotificationsChanged);
    };
  }, []);

  // Global keyboard shortcuts handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Debug log to see if events are being captured
      console.log('Key pressed:', event.key, 'Ctrl:', event.ctrlKey, 'Alt:', event.altKey, 'Shift:', event.shiftKey);

      // Handle ESC key for going back
      if (event.key === 'Escape' && !event.ctrlKey && !event.altKey) {
        event.preventDefault();
        console.log('ESC pressed - going back');
        window.history.back();
        return;
      }

      // Handle Ctrl + shortcuts for direct navigation
      if (event.ctrlKey && !event.altKey && !event.shiftKey) {
        const key = event.key.toLowerCase(); // Ensure lowercase for consistency

        console.log('Ctrl+' + key + ' pressed');

        // Prevent default browser shortcuts for our keys
        if (['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'a', 'b', 'c', 'e', 'g', 'i', 'j', 'k', 'o', 's'].includes(key)) {
          event.preventDefault();
          event.stopPropagation();
        }

        // Map shortcuts to navigation paths
        const shortcutMap: { [key: string]: string } = {
          '1': '/admin/multi-platform-orders',    // Restaurant Orders
          '2': '/admin/customer-orders',          // Customer Orders
          '3': '/admin/orders',                   // Manage Orders
          '4': '/admin/tables',                   // Manage Tables
          '5': '/admin/kot',                      // View KOTs
          '6': '/admin/inventory',                // Inventory
          '7': '/admin/menu-management',          // Menu Management
          '8': '/admin/bills',                    // Generate Bills
          '9': '/admin/sales',                    // Sales Analytics
          '0': '/admin/staff',                    // Staff Management
          'a': '/admin/attendance',               // Attendance
          'b': '/admin/notifications',            // Staff Notifications
          'c': '/admin/salary-management',        // Staff Salary
          'e': '/admin/reports',                  // Reports
          'g': '/admin/staff-pins',               // Staff PINs
          'i': '/admin/inventory',                // Inventory (alternative)
          'o': '/admin/offers',                   // Offers
          'j': '/admin/settings',                 // Settings
          'k': '/admin/backup'                    // Backup
        };

        const path = shortcutMap[key];
        if (path) {
          console.log(`🚀 Navigating to ${path} via shortcut Ctrl+${key}`);
          navigate(path);
        } else {
          console.log(`No shortcut found for Ctrl+${key}`);
        }
      }
    };

    // Add event listener to document with capture phase
    document.addEventListener('keydown', handleKeyDown, true);

    // Also ensure the document can receive focus
    if (document.body.tabIndex === -1) {
      document.body.tabIndex = 0;
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [navigate]);

  const loadPlatformStats = () => {
    const stats = multiPlatformOrderManager.getPlatformStats();
    setPlatformStats(stats);
  };

  const loadPendingNotifications = () => {
    const count = getPendingNotificationsCount();
    setPendingNotifications(count);
  };

  const loadCustomerOrdersCount = () => {
    // Sync orders from ordering app first
    customerOrderManager.refreshOrdersFromOrderingApp();
    const orders = customerOrderManager.getAllOrders();
    // Count new and accepted orders (active orders)
    const activeOrders = orders.filter(order =>
      order.status === 'new' || order.status === 'accepted' || order.status === 'preparing'
    );
    setCustomerOrdersCount(activeOrders.length);
  };

  // Mock data for dashboard stats
  const todayStats = {
    totalSales: 15420,
    totalOrders: 47,
    avgOrderValue: 328,
    completedOrders: 42,
    pendingOrders: 5,
    lowStockItems: 8,
    activeWaiters: 6,
    occupiedTables: 5
  };

  const recentOrders = [
    {
      id: "#ORD001",
      type: "Dine-in",
      table: "Table 3",
      amount: 850,
      status: "preparing",
      time: "2 mins ago",
      items: 4
    },
    {
      id: "#ORD002",
      type: "Delivery",
      customer: "Priya Sharma",
      amount: 520,
      status: "ready",
      time: "5 mins ago",
      items: 3
    },
    {
      id: "#ORD003",
      type: "Dine-in",
      table: "Table 7",
      amount: 1200,
      status: "completed",
      time: "15 mins ago",
      items: 6
    }
  ];

  const lowStockItems = [
    { name: "Chicken Biryani", stock: 5, minStock: 10, category: "Biryani" },
    { name: "Paneer Butter Masala", stock: 3, minStock: 8, category: "Veg Gravy" },
    { name: "Garlic Naan", stock: 12, minStock: 20, category: "Breads" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "preparing": return "bg-yellow-100 text-yellow-800";
      case "ready": return "bg-green-100 text-green-800";
      case "completed": return "bg-blue-100 text-blue-800";
      case "cancelled": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleLogout = () => {
    navigate("/delivery/login");
  };

  return (
    <div className="apk-page-container bg-gray-50 min-h-screen apk-layout-with-notifications">
      {/* Header */}
      <div className="bg-gradient-primary text-white shadow-sm apk-header-fixed">
        <div className="flex items-center justify-between p-3 sm:p-4">
          <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
            <Logo size="sm" variant="white" showText={true} className="shrink-0" />
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-white truncate">Admin Dashboard</h1>
              <p className="text-xs sm:text-sm text-white/80 truncate">Restaurant Management System</p>
            </div>
          </div>
          <div className="flex items-center gap-1 sm:gap-2 shrink-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsNotificationPanelVisible(!isNotificationPanelVisible)}
              className="text-white hover:bg-white/20 h-8 w-8 sm:h-10 sm:w-10 relative"
            >
              <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
              {pendingNotifications > 0 && (
                <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs bg-red-500 text-white rounded-full flex items-center justify-center">
                  {pendingNotifications > 9 ? '9+' : pendingNotifications}
                </Badge>
              )}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-white hover:bg-white/20 h-8 w-8 sm:h-10 sm:w-10"
            >
              <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleLogout}
              className="text-white hover:bg-white/20 h-8 w-8 sm:h-10 sm:w-10"
            >
              <LogOut className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className={`${isNotificationPanelVisible ? 'apk-main-content-with-notifications' : ''}`}>
        <div className="p-2 sm:p-4 space-y-3 sm:space-y-4 apk-content-with-header-no-gap">
        {/* Quick Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-blue-100 text-xs sm:text-sm">Today's Sales</p>
                  <p className="text-xl sm:text-2xl font-bold truncate">₹{todayStats.totalSales.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-xs">+12% from yesterday</span>
                  </div>
                </div>
                <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-blue-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-green-100 text-xs sm:text-sm">Total Orders</p>
                  <p className="text-xl sm:text-2xl font-bold">{todayStats.totalOrders}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-xs">+8% from yesterday</span>
                  </div>
                </div>
                <ShoppingCart className="h-6 w-6 sm:h-8 sm:w-8 text-green-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-purple-100 text-xs sm:text-sm">Avg Order Value</p>
                  <p className="text-xl sm:text-2xl font-bold truncate">₹{todayStats.avgOrderValue}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-xs">-3% from yesterday</span>
                  </div>
                </div>
                <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-purple-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-orange-100 text-xs sm:text-sm">Active Tables</p>
                  <p className="text-xl sm:text-2xl font-bold">{todayStats.occupiedTables}/8</p>
                  <div className="flex items-center gap-1 mt-1">
                    <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-xs">{todayStats.activeWaiters} waiters active</span>
                  </div>
                </div>
                <Utensils className="h-6 w-6 sm:h-8 sm:w-8 text-orange-200 shrink-0" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Settings className="h-4 w-4 sm:h-5 sm:w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm relative"
                onClick={() => navigate("/admin/multi-platform-orders")}
              >
                <Smartphone className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Restaurant Orders</span>
                {platformStats.reduce((sum, stat) => sum + stat.today.orders, 0) > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-red-500">
                    {platformStats.reduce((sum, stat) => sum + stat.today.orders, 0)}
                  </Badge>
                )}
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm relative"
                onClick={() => navigate("/admin/customer-orders")}
              >
                <Car className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Customer Orders</span>
                {customerOrdersCount > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-green-500">
                    {customerOrdersCount > 99 ? '99+' : customerOrdersCount}
                  </Badge>
                )}
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/orders")}
              >
                <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Manage Orders</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/tables")}
              >
                <Utensils className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Manage Tables</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/kot")}
              >
                <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>View KOTs</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/inventory")}
              >
                <Package className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Inventory</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/menu-management")}
              >
                <ChefHat className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Menu Management</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/bills")}
              >
                <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Generate Bills</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/sales")}
              >
                <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Sales Analytics</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/staff")}
              >
                <Users className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Staff Management</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/attendance")}
              >
                <Clock className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Attendance</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm relative"
                onClick={() => navigate("/admin/notifications")}
              >
                <Bell className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Staff Notifications</span>
                {pendingNotifications > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {pendingNotifications > 99 ? '99+' : pendingNotifications}
                  </Badge>
                )}
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/salary-management")}
              >
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Salary Management</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/reports")}
              >
                <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Reports</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/staff-pins")}
              >
                <Settings className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Staff PINs</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/offers")}
              >
                <Gift className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Offers</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/settings")}
              >
                <Settings className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Settings</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
                onClick={() => navigate("/admin/backup")}
              >
                <Package className="h-5 w-5 sm:h-6 sm:w-6" />
                <span>Backup</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm bg-blue-50 border-blue-200"
                onClick={() => {
                  runOrderIntegrationTest();
                  loadCustomerOrdersCount();
                  toast({
                    title: "Test Orders Created",
                    description: "Check browser console for details and refresh Customer Orders page",
                  });
                }}
              >
                <RefreshCw className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                <span className="text-blue-600">Test Orders</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Own App Order Stats */}
        {platformStats.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5" />
                Own App Order Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4">
                {platformStats.filter(stat => stat.platform === 'own-app').map((stat) => (
                  <div key={stat.platform} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-xl">
                        {stat.platform === 'own-app' && '🏪'}
                      </span>
                      <h4 className="font-medium capitalize">{stat.platform.replace('-', ' ')}</h4>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Today's Orders:</span>
                        <span className="font-medium">{stat.today.orders}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Revenue:</span>
                        <span className="font-medium">₹{stat.today.revenue}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Avg Order:</span>
                        <span className="font-medium">₹{Math.round(stat.today.avgOrderValue)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Recent Orders
              </CardTitle>
              <Button variant="outline" size="sm" onClick={() => navigate("/admin/orders")}>
                View All
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold text-sm">{order.id}</span>
                        <Badge className={`${getStatusColor(order.status)} text-xs px-2 py-1`}>
                          {order.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        {order.type} • {order.table || order.customer} • {order.items} items
                      </p>
                      <p className="text-xs text-gray-400">{order.time}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sm">₹{order.amount}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Low Stock Alert */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                Low Stock Alert
              </CardTitle>
              <Button variant="outline" size="sm" onClick={() => navigate("/admin/inventory")}>
                Manage Stock
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {lowStockItems.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex-1">
                      <p className="font-semibold text-sm">{item.name}</p>
                      <p className="text-sm text-gray-600">{item.category}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">
                        <span className="text-orange-600 font-bold">{item.stock}</span>
                        <span className="text-gray-400">/{item.minStock}</span>
                      </p>
                      <p className="text-xs text-orange-600">Low Stock</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        </div>
      </div>

      {/* Notification Panel */}
      <NotificationPanel
        isVisible={isNotificationPanelVisible}
        onToggle={() => setIsNotificationPanelVisible(!isNotificationPanelVisible)}
        onNotificationClick={(notification) => {
          // Handle notification click - navigate to relevant page
          if (notification.type === 'order' && notification.orderId) {
            navigate(`/admin/multi-platform-order/${notification.orderId}`);
          } else if (notification.type === 'inventory') {
            navigate('/admin/inventory');
          } else if (notification.type === 'staff') {
            navigate('/admin/staff');
          }
        }}
      />
    </div>
  );
};

export default AdminDashboard;
