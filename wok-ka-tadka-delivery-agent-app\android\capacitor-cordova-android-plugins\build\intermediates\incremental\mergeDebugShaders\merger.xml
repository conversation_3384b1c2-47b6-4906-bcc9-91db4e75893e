<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\capacitor-cordova-android-plugins\src\main\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\wok-ka-tadka-desktop-app\wok-ka-tadka-desktop-app\wok-ka-tadka-delivery-agent-app\android\capacitor-cordova-android-plugins\src\debug\shaders"/></dataSet></merger>